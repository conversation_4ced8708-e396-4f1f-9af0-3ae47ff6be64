<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Gamepad / Controller SVG Icons — Jermesa Studio</title>

  <!-- Google Font (SIL Open Font License) -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">

  <style>
    :root{
      --bg:#0f1720;
      --card:#0b1220;
      --muted:#98a0b3;
      --accent:#6ee7b7;
      --glass: rgba(255,255,255,0.03);
      --glass-2: rgba(255,255,255,0.02);
      --success:#16a34a;
      --danger:#ef4444;
      --shadow: 0 6px 18px rgba(2,6,23,0.6);
    }

    html,body{
      height:100%;
      margin:0;
      font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial;
      background: linear-gradient(180deg, #071021 0%, #081226 60%);
      color:#dbe7ff;
      -webkit-font-smoothing:antialiased;
      -moz-osx-font-smoothing:grayscale;
      line-height:1.35;
    }

    .wrap{
      max-width:1100px;
      margin:40px auto;
      padding:28px;
      background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01));
      border-radius:14px;
      box-shadow: var(--shadow);
      border:1px solid rgba(255,255,255,0.03);
    }

    header{
      display:flex;
      align-items:center;
      justify-content:space-between;
      gap:16px;
      margin-bottom:18px;
    }

    header h1{
      font-size:20px;
      margin:0;
      font-weight:600;
      letter-spacing:0.2px;
    }
    header p{
      margin:0;
      color:var(--muted);
      font-size:13px;
    }

    .grid{
      display:grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap:18px;
      margin-top:18px;
    }

    .card{
      background: linear-gradient(180deg,var(--card), #071226 120%);
      border-radius:12px;
      padding:14px;
      border:1px solid rgba(255,255,255,0.03);
      box-shadow: 0 6px 16px rgba(2,6,23,0.45);
      display:flex;
      gap:12px;
      align-items:flex-start;
    }

    .preview{
      width:160px;
      min-width:160px;
      display:flex;
      align-items:center;
      justify-content:center;
      padding:10px;
      background: linear-gradient(180deg,var(--glass), var(--glass-2));
      border-radius:10px;
      box-shadow: inset 0 1px 0 rgba(255,255,255,0.02);
    }

    .meta{
      flex:1;
      display:flex;
      flex-direction:column;
      gap:8px;
    }

    .meta h3{
      margin:0;
      font-size:15px;
      font-weight:600;
    }

    .meta p{
      margin:0;
      color:var(--muted);
      font-size:13px;
    }

    .controls{
      display:flex;
      gap:8px;
      align-items:center;
    }

    button.copyBtn{
      padding:8px 10px;
      border-radius:8px;
      border:0;
      background:linear-gradient(90deg,#0ea5a9,#60a5fa);
      color:#071226;
      font-weight:600;
      cursor:pointer;
      box-shadow: 0 6px 12px rgba(11,20,40,0.45);
    }

    button.copyBtn:active{ transform:translateY(1px); }
    .small{
      padding:7px 9px;
      font-size:13px;
      border-radius:8px;
      background:transparent;
      color:var(--muted);
      border:1px solid rgba(255,255,255,0.02);
      cursor:default;
    }

    pre.svgCode{
      margin:0;
      margin-top:8px;
      background:rgba(0,0,0,0.15);
      padding:10px;
      border-radius:8px;
      font-size:12px;
      overflow:auto;
      max-height:160px;
      color:#dbe7ff;
    }

    footer.attribution{
      margin-top:20px;
      padding-top:14px;
      border-top:1px dashed rgba(255,255,255,0.03);
      display:flex;
      justify-content:space-between;
      gap:12px;
      align-items:center;
      flex-wrap:wrap;
    }

    footer a{ color:var(--accent); text-decoration:none; font-weight:600; }
    footer small{ color:var(--muted); font-size:13px; }

    /* Cookie consent */
    .cookie-consent{
      position:fixed;
      left:18px;
      right:18px;
      bottom:18px;
      background: linear-gradient(180deg, rgba(2,6,23,0.9), rgba(2,6,23,0.95));
      border-radius:10px;
      padding:12px 14px;
      display:flex;
      align-items:center;
      gap:12px;
      box-shadow: 0 10px 30px rgba(2,6,23,0.7);
      border:1px solid rgba(255,255,255,0.02);
      z-index:1200;
      transform: translateY(0);
      transition: transform 280ms ease, opacity 280ms ease;
    }

    .cookie-consent.hidden{
      opacity:0;
      transform: translateY(24px);
      pointer-events:none;
    }

    .cookie-consent p{
      margin:0;
      color:var(--muted);
      font-size:13px;
    }
    .cookie-actions{
      margin-left:auto;
      display:flex;
      gap:8px;
      align-items:center;
    }

    .btn-accept{
      padding:8px 12px;
      border-radius:8px;
      background:var(--success);
      color:#052014;
      border:0;
      font-weight:600;
      cursor:pointer;
    }

    .btn-decline{
      padding:8px 12px;
      border-radius:8px;
      background:transparent;
      border:1px solid rgba(255,255,255,0.04);
      color:var(--muted);
      cursor:pointer;
    }

    @media (max-width:640px){
      .preview{ width:120px; min-width:120px; }
    }

  </style>
</head>
<body>
  <div class="wrap" role="main">
    <header>
      <div>
        <h1>Gamepad / Controller Icons (5 designs)</h1>
        <p>Modern multicolor + line SVGs — ready to copy. Created by Jermesa Studio.</p>
      </div>
      <div style="text-align:right">
        <small style="color:var(--muted)">Font: Inter (SIL Open Font License)</small>
      </div>
    </header>

    <section class="grid">

      <!-- ICON 1: Modern Pro Gamepad -->
      <article class="card" aria-labelledby="title1">
        <div class="preview">
          <!-- SVG 1 -->
          <svg id="svg1" width="220" height="120" viewBox="0 0 220 120" xmlns="http://www.w3.org/2000/svg" role="img" aria-labelledby="title1 desc1">
            <title id="title1">Modern Pro Gamepad</title>
            <desc id="desc1">Rounded modern pro controller with multicolor fills and outlined details</desc>
            <defs>
              <linearGradient id="g1" x1="0" x2="1">
                <stop offset="0" stop-color="#6ee7b7"/>
                <stop offset="1" stop-color="#60a5fa"/>
              </linearGradient>
              <linearGradient id="g1b" x1="0" x2="1">
                <stop offset="0" stop-color="#ffb86b"/>
                <stop offset="1" stop-color="#ff7ab6"/>
              </linearGradient>
              <filter id="s" x="-20%" y="-20%" width="140%" height="140%">
                <feDropShadow dx="0" dy="6" stdDeviation="8" flood-color="#000" flood-opacity="0.45"/>
              </filter>
            </defs>

            <!-- body -->
            <g filter="url(#s)">
              <path d="M30 60c0-20 22-36 40-36h80c18 0 40 16 40 36v6c0 22-18 40-40 40H70c-22 0-40-18-40-40v-6z"
                    fill="url(#g1)" opacity="0.95" stroke="#0b1220" stroke-width="2" />
              <path d="M38 62c0-15 18-28 32-28h72c14 0 32 13 32 28v4c0 18-14 32-32 32H70c-18 0-32-14-32-32v-4z"
                    fill="transparent" stroke="#0b1220" stroke-width="1.8" opacity="0.25"/>
            </g>

            <!-- left sticks -->
            <g transform="translate(58,62)">
              <circle cx="0" cy="0" r="12" fill="#071226" stroke="#e6eefc" stroke-width="2.2" />
              <circle cx="-1" cy="-1" r="4" fill="#9ca3ff" />
            </g>

            <!-- right sticks (as D-pad and buttons) -->
            <g transform="translate(162,48)">
              <rect x="-14" y="-14" width="28" height="28" rx="6" fill="url(#g1b)" stroke="#071226" stroke-width="1.6"/>
              <circle cx="-6" cy="-6" r="3.2" fill="#071226"/>
              <circle cx="6" cy="-6" r="3.2" fill="#071226"/>
              <circle cx="-6" cy="6" r="3.2" fill="#071226"/>
              <circle cx="6" cy="6" r="3.2" fill="#071226"/>
            </g>

            <!-- shoulder light -->
            <rect x="32" y="18" width="20" height="6" rx="3" fill="#8be0ff" opacity="0.9"/>

            <!-- subtle line details -->
            <path d="M95 35c18 0 28 8 34 18" stroke="rgba(255,255,255,0.06)" stroke-width="6" stroke-linecap="round" fill="none"/>

          </svg>
        </div>

        <div class="meta">
          <h3>Modern Pro Gamepad</h3>
          <p>Rounded pro controller with multicolor gradient body and outlined accents.</p>

          <div class="controls">
            <button class="copyBtn" onclick="copySvgToClipboard('svg1', this)">Copy SVG</button>
            <span class="small">SVG — Inline</span>
          </div>

          <pre id="code1" class="svgCode" readonly></pre>
        </div>
      </article>

      <!-- ICON 2: Retro Arcade Pad -->
      <article class="card" aria-labelledby="title2">
        <div class="preview">
          <!-- SVG 2 -->
          <svg id="svg2" width="200" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" role="img" aria-labelledby="title2 desc2">
            <title id="title2">Retro Arcade Pad</title>
            <desc id="desc2">Compact retro arcade style controller with joystick and two big buttons</desc>
            <defs>
              <linearGradient id="g2" x1="0" x2="1">
                <stop offset="0" stop-color="#f97316"/>
                <stop offset="1" stop-color="#ef4444"/>
              </linearGradient>
            </defs>

            <rect x="10" y="30" rx="12" ry="12" width="180" height="60" fill="#0b1220" stroke="#1f2937" stroke-width="1.6"/>
            <rect x="14" y="34" rx="10" ry="10" width="172" height="52" fill="#071226" stroke="rgba(255,255,255,0.02)"/>

            <!-- joystick -->
            <g transform="translate(60,60)">
              <circle cx="0" cy="0" r="18" fill="#111827" stroke="#e6eefc" stroke-width="2" />
              <circle cx="0" cy="-8" r="5.6" fill="#60a5fa"/>
            </g>

            <!-- big buttons -->
            <g transform="translate(130,48)">
              <circle cx="0" cy="0" r="14" fill="url(#g2)" stroke="#071226" stroke-width="1.8" />
            </g>
            <g transform="translate(160,72)">
              <circle cx="0" cy="0" r="12" fill="#a78bfa" stroke="#071226" stroke-width="1.6" />
            </g>

            <!-- line accents -->
            <path d="M34 44 L52 44" stroke="rgba(255,255,255,0.03)" stroke-width="8" stroke-linecap="round"/>
            <path d="M34 76 L52 76" stroke="rgba(255,255,255,0.03)" stroke-width="8" stroke-linecap="round"/>
          </svg>
        </div>

        <div class="meta">
          <h3>Retro Arcade Pad</h3>
          <p>Compact, arcade-styled block controller with joystick and large colorful buttons.</p>

          <div class="controls">
            <button class="copyBtn" onclick="copySvgToClipboard('svg2', this)">Copy SVG</button>
            <span class="small">SVG — Inline</span>
          </div>

          <pre id="code2" class="svgCode" readonly></pre>
        </div>
      </article>

      <!-- ICON 3: Minimal Handheld -->
      <article class="card" aria-labelledby="title3">
        <div class="preview">
          <!-- SVG 3 -->
          <svg id="svg3" width="220" height="120" viewBox="0 0 220 120" xmlns="http://www.w3.org/2000/svg" role="img" aria-labelledby="title3 desc3">
            <title id="title3">Minimal Handheld</title>
            <desc id="desc3">A sleek handheld device silhouette with colorful screen and simple controls</desc>
            <defs>
              <linearGradient id="g3" x1="0" x2="1">
                <stop offset="0" stop-color="#7dd3fc"/>
                <stop offset="1" stop-color="#a78bfa"/>
              </linearGradient>
            </defs>

            <rect x="10" y="20" width="200" height="80" rx="14" ry="14" fill="#071226" stroke="#0b1220" stroke-width="2"/>
            <rect x="26" y="34" width="120" height="52" rx="8" fill="url(#g3)" opacity="0.95"/>

            <!-- left controls -->
            <g transform="translate(160,50)">
              <rect x="-24" y="-10" width="14" height="24" rx="4" fill="#0b1220" stroke="#e6eefc" stroke-width="1.4"/>
              <rect x="10" y="-10" width="14" height="24" rx="4" fill="#0b1220" stroke="#e6eefc" stroke-width="1.4"/>
            </g>

            <!-- minimal lines -->
            <path d="M46 36c18 6 32 6 50 0" stroke="rgba(0,0,0,0.25)" stroke-width="6" stroke-linecap="round"/>
          </svg>
        </div>

        <div class="meta">
          <h3>Minimal Handheld</h3>
          <p>Sleek handheld silhouette with colorful display — ideal for UI and marketing mockups.</p>

          <div class="controls">
            <button class="copyBtn" onclick="copySvgToClipboard('svg3', this)">Copy SVG</button>
            <span class="small">SVG — Inline</span>
          </div>

          <pre id="code3" class="svgCode" readonly></pre>
        </div>
      </article>

      <!-- ICON 4: Joystick Classic -->
      <article class="card" aria-labelledby="title4">
        <div class="preview">
          <!-- SVG 4 -->
          <svg id="svg4" width="200" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" role="img" aria-labelledby="title4 desc4">
            <title id="title4">Joystick Classic</title>
            <desc id="desc4">A single-stick classic joystick with colorful base and thin line details</desc>
            <defs>
              <radialGradient id="g4" cx="50%" cy="40%" r="60%">
                <stop offset="0" stop-color="#fef3c7"/>
                <stop offset="1" stop-color="#fb7185"/>
              </radialGradient>
            </defs>

            <ellipse cx="100" cy="80" rx="72" ry="22" fill="#071226" opacity="0.9"/>
            <rect x="82" y="28" width="36" height="48" rx="8" fill="url(#g4)" stroke="#071226" stroke-width="1.4" />
            <circle cx="100" cy="40" r="7" fill="#0b1220" stroke="#fff" stroke-opacity="0.12" stroke-width="1.6"/>
            <path d="M96 76 L96 52" stroke="#0b1220" stroke-width="2.6" stroke-linecap="round"/>
            <path d="M104 76 L104 52" stroke="#0b1220" stroke-width="2.6" stroke-linecap="round"/>
          </svg>
        </div>

        <div class="meta">
          <h3>Joystick Classic</h3>
          <p>Single-stick arcade joystick with colorful base — retro but refined.</p>

          <div class="controls">
            <button class="copyBtn" onclick="copySvgToClipboard('svg4', this)">Copy SVG</button>
            <span class="small">SVG — Inline</span>
          </div>

          <pre id="code4" class="svgCode" readonly></pre>
        </div>
      </article>

      <!-- ICON 5: Slim Dual-Trigger -->
      <article class="card" aria-labelledby="title5">
        <div class="preview">
          <!-- SVG 5 -->
          <svg id="svg5" width="220" height="120" viewBox="0 0 220 120" xmlns="http://www.w3.org/2000/svg" role="img" aria-labelledby="title5 desc5">
            <title id="title5">Slim Dual-Trigger Controller</title>
            <desc id="desc5">Slim controller with pronounced triggers and colorful circular buttons</desc>
            <defs>
              <linearGradient id="g5" x1="0" x2="1">
                <stop offset="0" stop-color="#34d399"/>
                <stop offset="1" stop-color="#60a5fa"/>
              </linearGradient>
            </defs>

            <path d="M20 58c0-18 18-32 36-32h112c18 0 36 14 36 32v6c0 20-14 36-36 36H56c-22 0-36-16-36-36v-6z"
                  fill="#071226" stroke="rgba(255,255,255,0.03)" stroke-width="1.8"/>
            <path d="M48 50c0-10 12-18 22-18h80c10 0 22 8 22 18v4c0 10-8 18-22 18H70c-14 0-22-8-22-18v-4z"
                  fill="url(#g5)" opacity="0.95" stroke="#071226" stroke-width="1.6"/>

            <!-- triggers -->
            <rect x="20" y="34" rx="3" width="30" height="8" fill="#0ea5a9" opacity="0.9"/>
            <rect x="170" y="34" rx="3" width="30" height="8" fill="#60a5fa" opacity="0.9"/>

            <!-- circular button cluster -->
            <g transform="translate(154,66)">
              <circle cx="-14" cy="-6" r="6" fill="#f97316" />
              <circle cx="0" cy="-6" r="6" fill="#ef4444" />
              <circle cx="-7" cy="8" r="6" fill="#a78bfa" />
            </g>
          </svg>
        </div>

        <div class="meta">
          <h3>Slim Dual-Trigger</h3>
          <p>Thin-profile controller with colorful triggers and circular button cluster.</p>

          <div class="controls">
            <button class="copyBtn" onclick="copySvgToClipboard('svg5', this)">Copy SVG</button>
            <span class="small">SVG — Inline</span>
          </div>

          <pre id="code5" class="svgCode" readonly></pre>
        </div>
      </article>

    </section>

    <footer class="attribution">
      <div>
        <small>Font: <strong>Inter</strong> — SIL Open Font License (via Google Fonts)</small>
        <div style="margin-top:6px"><small>Icons and page created by <a href="https://www.jermesa.com" target="_blank" rel="noopener">Jermesa Studio</a></small></div>
      </div>

      <div style="text-align:right">
        <small>Privacy Policy: <a href="https://jermesa.com/privacy-policy/" target="_blank" rel="noopener">https://jermesa.com/privacy-policy/</a></small>
      </div>
    </footer>
  </div>

  <!-- Cookie consent UI -->
  <div id="cookieConsent" class="cookie-consent" role="dialog" aria-live="polite" aria-label="Cookie consent">
    <div>
      <p><strong>We use cookies</strong> — to improve your experience. By accepting you consent to our cookie usage and privacy policy.</p>
    </div>

    <div class="cookie-actions">
      <a href="https://jermesa.com/privacy-policy/" target="_blank" rel="noopener" class="btn-decline" style="text-decoration:none">Privacy</a>
      <button class="btn-accept" id="acceptCookies">Accept</button>
    </div>
  </div>

  <script>
    // Prepare each pre element with the serialized SVG markup
    function tidySvgString(svgEl) {
      // clone to remove interactive attributes if any, and pretty up spacing
      const clone = svgEl.cloneNode(true);
      // remove filter id collisions (we keep ids as-is here intentionally)
      // return outerHTML
      // Add XML namespace attribute if missing for copy/paste embedding
      if (!clone.getAttribute('xmlns')) {
        clone.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      }
      return clone.outerHTML;
    }

    function fillAllCodes() {
      for (let i = 1; i <= 5; i++) {
        const svg = document.getElementById('svg' + i);
        const pre = document.getElementById('code' + i);
        if (svg && pre) {
          // Pretty print a little by inserting line breaks for readability
          let sv = tidySvgString(svg);
          // Insert simple formatting: place tags on new lines
          sv = sv.replace(/></g, '>\n<');
          pre.textContent = sv;
        }
      }
    }

    function copySvgToClipboard(svgId, btnEl) {
      const svg = document.getElementById(svgId);
      if (!svg) return;
      const text = tidySvgString(svg);
      // Use Clipboard API
      navigator.clipboard.writeText(text).then(() => {
        const original = btnEl.textContent;
        btnEl.textContent = 'Copied ✓';
        btnEl.disabled = true;
        setTimeout(() => {
          btnEl.textContent = original;
          btnEl.disabled = false;
        }, 1600);
      }).catch((err) => {
        // Fallback: select pre content
        const pre = document.getElementById('code' + svgId.replace('svg',''));
        if (pre) {
          try {
            // create a temporary textarea
            const ta = document.createElement('textarea');
            ta.value = text;
            document.body.appendChild(ta);
            ta.select();
            document.execCommand('copy');
            document.body.removeChild(ta);
            const original = btnEl.textContent;
            btnEl.textContent = 'Copied ✓';
            btnEl.disabled = true;
            setTimeout(() => {
              btnEl.textContent = original;
              btnEl.disabled = false;
            }, 1600);
          } catch (e) {
            alert('Copy failed. You can manually copy from the code box below.');
          }
        } else {
          alert('Copy failed. You can manually copy from the code box below.');
        }
      });
    }

    // Cookie consent handling (persist acceptance in localStorage)
    (function() {
      const KEY = 'jermesa_cookie_accepted_v1';
      const consentEl = document.getElementById('cookieConsent');
      const accepted = localStorage.getItem(KEY);
      if (accepted === 'true') {
        consentEl.classList.add('hidden');
      } else {
        consentEl.classList.remove('hidden');
      }

      document.getElementById('acceptCookies').addEventListener('click', function() {
        localStorage.setItem(KEY, 'true');
        // animate hide
        consentEl.classList.add('hidden');
      });
    })();

    // Fill code boxes on DOM ready
    document.addEventListener('DOMContentLoaded', function() {
      fillAllCodes();
    });
  </script>
</body>
</html>
